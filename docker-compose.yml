version: '3.8'

services:
  sw-ai-agents:
    build: .
    container_name: sw-ai-agents
    environment:
      # Jira Configuration
      - JIRA_SERVER_URL=${JIRA_SERVER_URL:-https://jira.skywindgroup.com}
      - JIRA_CREDENTIALS=${JIRA_CREDENTIALS}

      # Claude Configuration
      - CLAUDE_CODE_OAUTH_TOKEN=${CLAUDE_CODE_OAUTH_TOKEN}

      # Bitbucket Configuration (if needed)
      - BITBUCKET_SERVER_URL=${BITBUCKET_SERVER_URL}
      - BITBUCKET_CREDENTIALS=${BITBUCKET_CREDENTIALS}

      # Tool Configuration
      - INPUT_ALLOWED_TOOLS=${INPUT_ALLOWED_TOOLS}
      - INPUT_DISALLOWED_TOOLS=${INPUT_DISALLOWED_TOOLS}

      # Repository Configuration
      - REPOS_DIR=/app/repos

    volumes:
      # Mount repos directory to persist git repositories
      - ./repos:/app/repos
      # Mount temp directory for <PERSON> prompts
      - /tmp/claude-prompts:/tmp/claude-prompts

    # Override the default command to run with a specific issue ID
    # Example: docker-compose run sw-ai-agents --issue-id PROJ-123
    # command: ["--help"]

    # Uncomment if you need to keep the container running
    stdin_open: true
    tty: true
