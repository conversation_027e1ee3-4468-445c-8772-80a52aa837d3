pipeline {
  agent any
  options {
    disableConcurrentBuilds()
    timestamps()
    timeout(time: 1, unit: 'HOURS')
  }
  environment {
    REVISION = sh(returnStdout: true, script: 'git rev-parse HEAD').trim()
    BRANCH_NAME_NORMALIZED = "${BRANCH_NAME.toLowerCase().replace("/", "_")}"
    REPO = "skywindgroup"
    SERVICE = "sw-ai-agents"
    REGION = "asia.gcr.io"
    PROJECT = "gcpstg"
  }

  stages {
    stage('Build Docker image') {
      when {
        anyOf {
          branch 'develop'
          branch 'ai'
        }
      }
      steps {
        sh "docker build --no-cache -f Dockerfile -t ${REPO}/${SERVICE}:${BRANCH_NAME_NORMALIZED} ."
      }
    }
    stage('Tag image') {
      when {
        anyOf {
          branch 'develop'
          branch 'ai'
        }
      }
      steps {
        sh 'docker tag ${REPO}/${SERVICE}:${BRANCH_NAME_NORMALIZED} ${REGION}/${PROJECT}/${SERVICE}:latest'
        sh 'docker tag ${REPO}/${SERVICE}:${BRANCH_NAME_NORMALIZED} ${REGION}/${PROJECT}/${SERVICE}:${REVISION}'
      }
    }
    stage('Push image') {
      when {
        anyOf {
          branch 'develop'
          branch 'ai'
        }
      }
      steps {
        sh 'docker push ${REPO}/${SERVICE}:latest'
        sh 'docker push ${REGION}/${PROJECT}/${SERVICE}:latest'
        sh 'docker push ${REGION}/${PROJECT}/${SERVICE}:${REVISION}'
      }
    }
  }
}
