pipeline {
  agent any

  parameters {
    string(
      name: 'JIRA_ISSUE_ID',
      defaultValue: 'SWS-34515',
      description: 'JIRA Issue ID (e.g., PROJ-123)',
      trim: true
    )
  }

  environment {
    JIRA_CREDENTIALS_ID = 'CMBuilderSkywindCrenedtials'
    JIRA_SERVER_URL = 'https://jira.skywindgroup.com'
    NODE = "jod"
  }

  stages {
    stage('Validate Parameters') {
      steps {
        script {
          if (!params.JIRA_ISSUE_ID) {
              error("JIRA_ISSUE_ID parameter is required. Please provide a valid JIRA issue ID (e.g., PROJ-123)")
          }
          if (!params.JIRA_ISSUE_ID.matches('^[A-Z]+-[0-9]+$')) {
              error("Invalid JIRA Issue ID format: '${params.JIRA_ISSUE_ID}'. Expected format: PROJECT-NUMBER (e.g., PROJ-123)")
          }
          echo "✓ JIRA Issue ID: ${params.JIRA_ISSUE_ID}"
        }
      }
    }

    stage('Setup Repositories') {
      steps {
        script {
          def project = params.JIRA_ISSUE_ID.split('-')[0]
          echo "✓ Extracted PROJECT: ${project}"

          def repositoriesFile = 'src/repositories.json'
          if (!fileExists(repositoriesFile)) {
            error("repositories.json file not found at: ${repositoriesFile}")
          }

          def repositoriesJson = readJSON file: repositoriesFile
          echo "✓ Loaded repositories configuration"

          def matchingRepos = []
          repositoriesJson.each { repoUrl, projects ->
            if (projects.contains(project)) {
              matchingRepos.add(repoUrl)
            }
          }

          if (matchingRepos.isEmpty()) {
            echo "⚠ No repositories found for project: ${project}"
          } else {
            echo "✓ Found ${matchingRepos.size()} repositories for project ${project}:"
            matchingRepos.each { repo ->
              echo "  - ${repo}"
            }

            def homeDir = sh(script: 'echo $HOME', returnStdout: true).trim()
            echo "✓ Home directory: ${homeDir}"

            matchingRepos.each { repoUrl ->
              def repoName = repoUrl.tokenize('/').last().replace('.git', '')
              def targetDir = "${homeDir}/${repoName}"

              echo "Cloning ${repoUrl} to ${targetDir}"

              sh """
                if [ -d "${targetDir}" ]; then
                  echo "Repository ${repoName} already exists, updating..."
                  cd "${targetDir}"
                  git fetch --all
                  git reset --hard origin/main || git reset --hard origin/master
                else
                  echo "Cloning repository ${repoName}..."
                  git clone "${repoUrl}" "${targetDir}"
                fi
              """

              echo "✓ Repository ${repoName} ready at ${targetDir}"
            }
          }
        }
      }
    }

    stage('NPM install') {
      steps {
        withDockerContainer(image: "node:${NODE}", toolName: 'latest') {
          configFileProvider([configFile(fileId: 'npm-npmjs', targetLocation: '.npmrc')]) {
            sh 'touch .env'
            sh 'touch package-lock.json'
            sh 'rm package-lock.json'
            sh 'rm -rf node_modules'
            sh 'npm i'
          }
        }
      }
    }
  }

  post {
    always {
      echo "=== Pipeline Execution Summary ==="
      echo "JIRA Issue ID: ${params.JIRA_ISSUE_ID}"
      echo "JIRA Server: ${env.JIRA_SERVER_URL}"
      echo "Build Status: ${currentBuild.currentResult}"
      echo "Build Duration: ${currentBuild.durationString}"
    }

    success {
      echo "✓ Pipeline completed successfully"
      echo "JIRA issue '${params.JIRA_ISSUE_ID}' details retrieved and displayed"
    }

    failure {
      echo "✗ Pipeline failed"
      echo "Please check the logs above for error details"
    }
  }
}
