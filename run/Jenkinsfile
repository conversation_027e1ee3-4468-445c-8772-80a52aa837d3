pipeline {
  agent any

  parameters {
    string(
      name: 'JIRA_ISSUE_ID',
      defaultValue: 'SWS-34515',
      description: 'JIRA Issue ID (e.g., PROJ-123)',
      trim: true
    )
  }

  environment {
    JIRA_CREDENTIALS_ID = 'CMBuilderSkywindCrenedtials'
    JIRA_SERVER_URL = 'https://jira.skywindgroup.com'
    REPO = "skywindgroup"
    SERVICE = "sw-ai-agents"
    REGION = "asia.gcr.io"
    PROJECT = "gcpstg"
  }

  stages {
    stage('Validate Parameters') {
      steps {
        script {
          if (!params.JIRA_ISSUE_ID) {
              error("JIRA_ISSUE_ID parameter is required. Please provide a valid JIRA issue ID (e.g., PROJ-123)")
          }
          if (!params.JIRA_ISSUE_ID.matches('^[A-Z]+-[0-9]+$')) {
              error("Invalid JIRA Issue ID format: '${params.JIRA_ISSUE_ID}'. Expected format: PROJECT-NUMBER (e.g., PROJ-123)")
          }
          echo "✓ JIRA Issue ID: ${params.JIRA_ISSUE_ID}"
        }
      }
    }

    stage('Run') {
      steps {
        withDockerContainer(image: "node:${NODE}", toolName: 'latest') {
          configFileProvider([configFile(fileId: 'npm-npmjs', targetLocation: '.npmrc')]) {
            sh 'touch .env'
            sh 'touch package-lock.json'
            sh 'rm package-lock.json'
            sh 'rm -rf node_modules'
            sh 'npm i'
          }
        }
      }
    }
  }

  post {
    always {
      echo "=== Pipeline Execution Summary ==="
      echo "JIRA Issue ID: ${params.JIRA_ISSUE_ID}"
      echo "JIRA Server: ${env.JIRA_SERVER_URL}"
      echo "Build Status: ${currentBuild.currentResult}"
      echo "Build Duration: ${currentBuild.durationString}"
    }

    success {
      echo "✓ Pipeline completed successfully"
      echo "JIRA issue '${params.JIRA_ISSUE_ID}' details retrieved and displayed"
    }

    failure {
      echo "✗ Pipeline failed"
      echo "Please check the logs above for error details"
    }
  }
}
